import React from 'react';
/**
 * ERROR HANDLING FIX: Comprehensive Error Handling Hook
 * Provides unified error handling for React components
 */

import { useState, useCallback, useEffect, useRef } from 'react'
import { useErrorHandler } from '@/utils/comprehensiveErrorHandler'
import { useUXFeedback } from '@/utils/uxFeedbackManager'

export interface ErrorState {
  hasError: boolean
  error: Error | null
  errorId: string | null
  retryCount: number
  isRetrying: boolean
}

export interface ErrorHandlingOptions {
  language?: 'ar' | 'en'
  maxRetries?: number
  retryDelay?: number
  showToast?: boolean
  logErrors?: boolean
  component?: string
}

export interface AsyncErrorOptions extends ErrorHandlingOptions {
  loadingMessage?: string
  successMessage?: string
  errorMessage?: string
}

export function useErrorHandling(options: ErrorHandlingOptions = {} as any) {
  const {
    language = 'en',
    maxRetries = 3,
    retryDelay = 1000,
    showToast = true,
    logErrors = true,
    component = 'Unknown'
  } = options

  const [errorState, setErrorState] = useState<ErrorState>({
    hasError: false,
    error: null,
    errorId: null,
    retryCount: 0,
    isRetrying: false
  })

  const { handleError: handleErrorComprehensive } = useErrorHandler(language as any)
  const uxFeedback = useUXFeedback(language as any)
  // @ts-ignore
  const retryTimeoutRef = useRef<(NodeJS as any).Timeout>()

  // ERROR FIX: Clear error state
  const clearError = useCallback(() => {
    setErrorState({
      hasError: false,
      error: null,
      errorId: null,
      retryCount: 0,
      isRetrying: false
    })

    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current)
    }
  }, [])

  // ERROR FIX: Handle error with comprehensive processing
  const handleError = useCallback((
    error: unknown,
    context: string = 'unknown',
    customOptions: Partial<ErrorHandlingOptions> = {}
   // @ts-ignore
   as any) => {
    const mergedOptions = { ...options, ...customOptions }
    
    const processedError = handleErrorComprehensive(error, {
      component,
      action: context,
      additionalData: {
        retryCount: (errorState as any as any).retryCount,
        // @ts-ignore
        timestamp: new Date( as any).toISOString( as any)
      }
    }, {
      language: (mergedOptions as any).language,
      showToast: (mergedOptions as any).showToast,
      logToConsole: (mergedOptions as any).logErrors
    })

    setErrorState(prev => ({
      hasError: true,
      error: error instanceof Error ? error : new Error(String(error as any)),
      errorId: (processedError as any).code,
      retryCount: (prev as any).retryCount,
      isRetrying: false
    }))

    return processedError
  }, [component, (errorState as any).retryCount, handleErrorComprehensive, options])

  // ERROR FIX: Retry with exponential backoff
  const retry = useCallback(async (
    // @ts-ignore
    retryFn: ( as any) => Promise<any> | any,
    // @ts-ignore
    context: string = 'retry'
  // @ts-ignore
  ) => {
    if ((errorState as any).retryCount >= maxRetries) {
      (uxFeedback as any).error(
        language === 'ar' 
          ? 'تم تجاوز الحد الأقصى لعدد المحاولات' 
          : 'Maximum retry attempts exceeded'
       // @ts-ignore
       as any)
      return
    }

    setErrorState(prev => ({
      ...prev,
      isRetrying: true,
      retryCount: (prev as any as any).retryCount + 1
    }))

    const delay = retryDelay * (Math as any).pow(2, (errorState as any as any).retryCount) // Exponential backoff

    // @ts-ignore
    (retryTimeoutRef as any).current = setTimeout(async ( as any) => {
      try {
        // @ts-ignore
        const result = await retryFn( as any)
        // @ts-ignore
        clearError( as any)
        
        (uxFeedback as any).success(
          language === 'ar' 
            ? 'تم حل المشكلة بنجاح' 
            : 'Issue resolved successfully'
         // @ts-ignore
         as any)
        
        return result
      } catch (retryError) {
        handleError(retryError, `${context}_retry_${(errorState as any as any).retryCount + 1}`)
      }
    // @ts-ignore
    }, delay)
  // @ts-ignore
  }, [(errorState as any).retryCount, maxRetries, retryDelay, language, uxFeedback, clearError, handleError])

  // ERROR FIX: Async operation wrapper with error handling
  const withErrorHandling = useCallback(<T>(
    // @ts-ignore
    asyncFn: ( as any) => Promise<T>,
    // @ts-ignore
    context: string = 'async_operation',
    // @ts-ignore
    asyncOptions: AsyncErrorOptions = {}
  // @ts-ignore
  ) => {
    return async (): Promise<T | null> => {
      const {
        loadingMessage,
        successMessage,
        errorMessage
      } = asyncOptions

      let loadingId: string | undefined

      try {
        // Clear previous errors
        // @ts-ignore
        clearError( as any)

        // Show loading feedback
        if (loadingMessage) {
          loadingId = (uxFeedback as any).loading(loadingMessage as any)
        }

        // @ts-ignore
        const result = await asyncFn( as any)

        // Show success feedback
        if (successMessage) {
          if (loadingId) {
            (uxFeedback as any).dismissLoading(loadingId, 'success', successMessage as any)
          } else {
            (uxFeedback as any).success(successMessage as any)
          }
        } else if (loadingId) {
          (uxFeedback as any).dismissLoading(loadingId as any)
        }

        return result
      } catch (error) {
        // Handle error
        const processedError = handleError(error, context, asyncOptions as any)

        // Show error feedback
        const finalErrorMessage = errorMessage || (processedError as any).userMessage
        if (loadingId) {
          (uxFeedback as any).dismissLoading(loadingId, 'error', finalErrorMessage as any)
        }

        return null
      }
    }
  // @ts-ignore
  }, [clearError, uxFeedback, handleError])

  // ERROR FIX: Safe async execution with automatic retry
  const safeAsync = useCallback(async <T>(
    // @ts-ignore
    asyncFn: ( as any) => Promise<T>,
    // @ts-ignore
    context: string = 'safe_async',
    // @ts-ignore
    autoRetry: boolean = false
  // @ts-ignore
  ): Promise<T | null> => {
    try {
      // @ts-ignore
      clearError( as any)
      // @ts-ignore
      return await asyncFn( as any)
    } catch (error) {
      const processedError = handleError(error, context as any)
      
      // Auto retry for retryable errors
      if (autoRetry && (processedError as any).retryable && (errorState as any).retryCount < maxRetries) {
        return retry(asyncFn, context as any)
      }
      
      return null
    }
  // @ts-ignore
  }, [clearError, handleError, (errorState as any).retryCount, maxRetries, retry])

  // ERROR FIX: Form submission with error handling
  const handleFormSubmit = useCallback(<T>(
    // @ts-ignore
    submitFn: ( as any) => Promise<T>,
    // @ts-ignore
    options: {
      successMessage?: string
      // @ts-ignore
      errorMessage?: string
      // @ts-ignore
      onSuccess?: (result: T) => void
      // @ts-ignore
      onError?: (error: any) => void
    // @ts-ignore
    } = {}
  // @ts-ignore
  ) => {
    return withErrorHandling(
      // @ts-ignore
      async ( as any) => {
        // @ts-ignore
        const result = await submitFn( as any)
        (options as any).onSuccess?.(result)
        // @ts-ignore
        return result
      },
      'form_submit',
      {
        loadingMessage: language === 'ar' ? 'جاري الحفظ...' : 'Saving...',
        successMessage: (options as any).successMessage || (language === 'ar' ? 'تم الحفظ بنجاح' : 'Saved successfully'),
        errorMessage: (options as any).errorMessage
      }
    )
  // @ts-ignore
  }, [withErrorHandling, language])

  // ERROR FIX: Data fetching with error handling
  const handleDataFetch = useCallback(<T>(
    // @ts-ignore
    fetchFn: ( as any) => Promise<T>,
    // @ts-ignore
    options: {
      dataType?: string
      // @ts-ignore
      silent?: boolean
      // @ts-ignore
      onSuccess?: (data: T) => void
      // @ts-ignore
      onError?: (error: any) => void
    // @ts-ignore
    } = {}
  // @ts-ignore
  ) => {
    const { dataType = 'data', silent = false } = options

    return withErrorHandling(
      // @ts-ignore
      async ( as any) => {
        // @ts-ignore
        const data = await fetchFn( as any)
        (options as any).onSuccess?.(data)
        // @ts-ignore
        return data
      },
      'data_fetch',
      {
        loadingMessage: silent ? undefined : (
          language === 'ar' ? `جاري تحميل ${dataType}...` : `Loading ${dataType}...`
        ),
        errorMessage: language === 'ar' 
          ? `فشل في تحميل ${dataType}` 
          : `Failed to load ${dataType}`
      }
    )
  // @ts-ignore
  }, [withErrorHandling, language])

  // ERROR FIX: Cleanup on unmount
  // @ts-ignore
  useEffect(( as any) => {
    return () => {
      if ((retryTimeoutRef as any).current) {
        clearTimeout((retryTimeoutRef as any as any).current)
      }
    }
  // @ts-ignore
  }, [])

  return {
    // State
    errorState,
    hasError: (errorState as any).hasError,
    error: (errorState as any).error,
    errorId: (errorState as any).errorId,
    retryCount: (errorState as any).retryCount,
    isRetrying: (errorState as any).isRetrying,

    // Actions
    handleError,
    clearError,
    retry,

    // Wrappers
    withErrorHandling,
    safeAsync,
    handleFormSubmit,
    handleDataFetch,

    // Utilities
    canRetry: (errorState as any).retryCount < maxRetries,
    isRetryable: (error: any) => {
      // Check if error is retryable based on type
      if (error?.code?.includes('network' as any) || error?.code?.includes('timeout' as any)) {
        return true
      }
      if (error?.status >= 500) {
        return true
      }
      return false
    }
  }
}

// ERROR FIX: Specialized hooks for common use cases
export function useFormErrorHandling(language: 'ar' | 'en' = 'en' as any) {
  return useErrorHandling({
    language,
    component: 'Form',
    maxRetries: 2,
    showToast: true
  } as any)
}

export function useApiErrorHandling(language: 'ar' | 'en' = 'en' as any) {
  return useErrorHandling({
    language,
    component: 'API',
    maxRetries: 3,
    retryDelay: 2000,
    showToast: true
  } as any)
}

export function useDataErrorHandling(language: 'ar' | 'en' = 'en' as any) {
  return useErrorHandling({
    language,
    component: 'Data',
    maxRetries: 2,
    showToast: false, // Usually handled by loading states
    logErrors: true
  } as any)
}

export default useErrorHandling
