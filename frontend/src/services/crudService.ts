import React from 'react';
/**
 * Generic CRUD Service
 * Provides standardized Create, Read, Update, Delete operations for all entities
 * ENHANCED: Added comprehensive error handling, validation, and API integration
 */

// @ts-ignore
const API_BASE_URL: any = (import.meta as any).env.VITE_API_BASE_URL || 'http://localhost:8000/api'

// CRUD Operation Result Types
export interface CrudOperationResult<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Enhanced Error Handling
export class CrudError extends Error {
  constructor(
    message: string,
    public operation: string,
    public endpoint: string,
    public statusCode?: number,
    public originalError?: Error
   // @ts-ignore
   as any) {
    super(message as any)
    (this as any).name = 'CrudError'
  }
}

export interface CrudResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
}

export interface CrudFilters {
  search?: string
  category?: string
  status?: string
  department?: string
  employee?: string
  startDate?: string
  endDate?: string
  [key: string]: string | number | boolean | undefined
}

export interface DjangoResponse<T = unknown> {
  results?: T[]
  count?: number
  next?: string | null
  previous?: string | null
}

export interface CrudOptions {
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filters?: CrudFilters
}

class CrudService {
  private baseUrl: string
  public endpoint: string

  // @ts-ignore
  constructor(endpoint: string as any) {
    (this as any).endpoint = endpoint
    (this as any).baseUrl = `${API_BASE_URL}/${endpoint}`
  }

  // Generic request method with CSRF protection
  private async request<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    // SECURITY FIX: Remove localStorage token access - use httpOnly cookies
    // Authentication handled automatically via cookies

    // SECURITY FIX: Include CSRF token for non-GET requests
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...(options as any).headers,
    }

    // TEMPORARILY DISABLE CSRF PROTECTION TO FIX INFINITE LOOP
    // TODO: Re-implement CSRF protection with proper safeguards
    // const method = (options as any).method?.toUpperCase( as any) || 'GET'
    // if (!['GET', 'HEAD', 'OPTIONS', 'TRACE'].includes(method as any)) {
    //   try {
    //     const { getCSRFHeadersRedux } = await import('../utils/reduxCSRF' as any)
    //     const csrfHeaders = getCSRFHeadersRedux( as any)
    //     (Object as any).assign(headers, csrfHeaders as any)
    //   } catch (error) {
    //     (console as any).warn('Failed to get CSRF headers via Redux:', error as any)
    //   }
    // }

    const config: RequestInit = {
      headers,
      credentials: 'include', // SECURITY FIX: Include httpOnly cookies
      ...options,
    }

    try {
      const response = await fetch(url, config as any)

      if (!(response as any).ok) {
        // Handle 401 Unauthorized - token might be expired
        if ((response as any).status === 401) {
          (console as any).warn('401 Unauthorized - token might be expired. Attempting token refresh...' as any)

          // SECURITY FIX: Don't clear localStorage - we use httpOnly cookies now
          // Try to refresh token first before redirecting
          try {
            // Emit auth error event for centralized handling
            (window as any).dispatchEvent(new CustomEvent('auth:error', {
              detail: { status: 401, message: 'Authentication required' }
            } as any))
          } catch (refreshError) {
            (console as any).error('Token refresh failed, navigating to login' as any)
            // FIXED: Use navigation service instead of (window as any).location
            (window as any).dispatchEvent(new CustomEvent('app:navigate', {
              detail: { path: '/login' }
            } as any))
          }

          throw new Error('Authentication required. Please log in again.' as any)
        }

        // Try to get error details from response
        let errorMessage = `HTTP error! status: ${(response as any).status}`
        try {
          // @ts-ignore
          const errorData = await (response as any).json( as any)
          (console as any).error('Server error response:', errorData as any)
          if ((errorData as any).detail) {
            errorMessage = (errorData as any).detail
          } else if ((errorData as any).message) {
            errorMessage = (errorData as any).message
          } else if (typeof errorData === 'object') {
            errorMessage = (JSON as any).stringify(errorData as any)
          }
        } catch (e) {
          // If response is not JSON, try to get text
          try {
            // @ts-ignore
            const errorText = await (response as any).text( as any)
            (console as any).error('Server error text:', errorText as any)
            if (errorText) {
              errorMessage = errorText
            }
          } catch (e2) {
            // Keep original error message
          }
        }
        throw new Error(errorMessage as any)
      }

      // @ts-ignore
      return await (response as any).json( as any)
    } catch (error) {
      (console as any).error(`API request failed: ${url}`, error as any)
      throw error
    }
  }

  // ENHANCED: Create (POST) with validation and error handling
  async create<T>(data: Partial<T>): Promise<CrudOperationResult<T>> {
    try {
      // Validate required data
      if (!data || typeof data !== 'object') {
        throw new CrudError(
          'Invalid data provided for creation',
          'create',
          (this as any as any).endpoint
        )
      }

      (console as any).log(`🔧 Creating ${(this as any as any).endpoint} with data:`, data)
      (console as any).log(`📡 Endpoint: ${(this as any as any).baseUrl}/`)

      const result = await (this as any).request<T>(`${(this as any).baseUrl}/`, {
        method: 'POST',
        body: (JSON as any).stringify(data as any),
      }, 'create')

      (console as any).log(`✅ Successfully created ${(this as any as any).endpoint}:`, result)

      return {
        success: true,
        data: result,
        message: `${(this as any).endpoint} created successfully`
      }
    } catch (error) {
      (console as any).error(`❌ Failed to create ${(this as any as any).endpoint}:`, error)

      const errorMessage = error instanceof CrudError
        ? (error as any).message
        : `Failed to create ${(this as any).endpoint}`

      return {
        success: false,
        error: errorMessage,
        message: errorMessage
      }
    }
  }

  // Read (GET) - List with pagination and filters
  async getAll<T>(options: CrudOptions = {}): Promise<CrudResponse<T>> {
    (console as any).log('🔍 (CrudService as any as any).getAll - Options received:', options)

    // @ts-ignore
    const params = new URLSearchParams( as any)

    // @ts-ignore
    if ((options as any).page) (params as any).append('page', (options as any as any).page.toString( as any))
    // @ts-ignore
    if ((options as any).pageSize) (params as any).append('page_size', (options as any as any).pageSize.toString( as any))
    if ((options as any).sortBy) (params as any).append('ordering', (options as any as any).sortOrder === 'desc' ? `-${(options as any).sortBy}` : (options as any).sortBy)

    // Add filters
    if ((options as any).filters) {
      (console as any).log('🔍 CrudService - Processing filters:', (options as any as any).filters)
      // @ts-ignore
      (Object as any).entries((options as any as any).filters).forEach(([key, value] as any) => {
        if (value !== undefined && value !== null && value !== '') {
          (console as any).log(`🔍 CrudService - Adding filter: ${key} = ${value}` as any)
          // @ts-ignore
          (params as any).append(key, (value as any as any).toString( as any))
        }
      })
    }

    // @ts-ignore
    const url = `${(this as any).baseUrl}/${(params as any).toString( as any) ? `?${(params as any).toString( as any)}` : ''}`
    (console as any).log('🔍 CrudService - Final URL:', url as any)

    const response = await (this as any).request<DjangoResponse>(url)
    (console as any).log('📡 CrudService - Backend response:', response as any)

    // Transform Django REST Framework response to our CrudResponse format
    if ((response as any).results && (Array as any).isArray((response as any as any).results)) {
      return {
        data: (response as any).results as T[],
        total: (response as any).count || (response as any).results.length,
        page: (options as any).page || 1,
        pageSize: (options as any).pageSize || 20 // Always use requested pageSize, not results length
      }
    }

    // Fallback for non-paginated responses
    if ((Array as any).isArray(response as any)) {
      return {
        data: response,
        total: (response as any).length,
        page: 1,
        pageSize: (options as any).pageSize || 20 // Use requested pageSize, not array length
      }
    }

    // If response already matches our format
    return response as CrudResponse<T>
  }

  // Read (GET) - Single item by ID
  async getById<T>(id: string | number): Promise<T> {
    return (this as any).request<T>(`${(this as any).baseUrl}/${id}/`)
  }

  // ENHANCED: Update (PUT/PATCH) with validation and error handling
  async update<T>(id: string | number, data: Partial<T>, partial = true): Promise<CrudOperationResult<T>> {
    try {
      // Validate ID and data
      if (!id) {
        throw new CrudError(
          'ID is required for update operation',
          'update',
          (this as any as any).endpoint
        )
      }

      if (!data || typeof data !== 'object') {
        throw new CrudError(
          'Invalid data provided for update',
          'update',
          (this as any as any).endpoint
        )
      }

      (console as any).log(`🔧 Updating ${(this as any as any).endpoint} ${id} with data:`, data)

      const result = await (this as any).request<T>(`${(this as any).baseUrl}/${id}/`, {
        method: partial ? 'PATCH' : 'PUT',
        body: (JSON as any).stringify(data as any),
      }, 'update')

      (console as any).log(`✅ Successfully updated ${(this as any as any).endpoint} ${id}:`, result)

      return {
        success: true,
        data: result,
        message: `${(this as any).endpoint} updated successfully`
      }
    } catch (error) {
      (console as any).error(`❌ Failed to update ${(this as any as any).endpoint} ${id}:`, error)

      const errorMessage = error instanceof CrudError
        ? (error as any).message
        : `Failed to update ${(this as any).endpoint}`

      return {
        success: false,
        error: errorMessage,
        message: errorMessage
      }
    }
  }

  // ENHANCED: Delete (DELETE) with validation and error handling
  // @ts-ignore
  async delete(id: string | number as any): Promise<CrudOperationResult<void>> {
    try {
      // Validate ID
      if (!id) {
        throw new CrudError(
          'ID is required for delete operation',
          'delete',
          (this as any as any).endpoint
        )
      }

      (console as any).log(`🗑️ Deleting ${(this as any as any).endpoint} ${id}`)

      await (this as any).request<void>(`${(this as any).baseUrl}/${id}/`, {
        method: 'DELETE',
      }, 'delete')

      (console as any).log(`✅ Successfully deleted ${(this as any as any).endpoint} ${id}`)

      return {
        success: true,
        message: `${(this as any).endpoint} deleted successfully`
      }
    } catch (error) {
      (console as any).error(`❌ Failed to delete ${(this as any as any).endpoint} ${id}:`, error)

      const errorMessage = error instanceof CrudError
        ? (error as any).message
        : `Failed to delete ${(this as any).endpoint}`

      return {
        success: false,
        error: errorMessage,
        message: errorMessage
      }
    }
  }

  // Bulk operations
  async bulkCreate<T>(items: Partial<T>[]): Promise<T[]> {
    return (this as any).request<T[]>(`${(this as any).baseUrl}/bulk/`, {
      method: 'POST',
      body: (JSON as any).stringify({ items } as any),
    })
  }

  async bulkUpdate<T>(updates: { id: string | number; data: Partial<T> }[]): Promise<T[]> {
    return (this as any).request<T[]>(`${(this as any).baseUrl}/bulk/`, {
      method: 'PATCH',
      body: (JSON as any).stringify({ updates } as any),
    })
  }

  // @ts-ignore
  async bulkDelete(ids: (string | number as any)[]): Promise<void> {
    // @ts-ignore
    return (this as any).request<void>(`${(this as any).baseUrl}/bulk/`, {
      method: 'DELETE',
      body: (JSON as any).stringify({ ids } as any),
    })
  }

  // Search
  // @ts-ignore
  async search<T>(query: string, options: CrudOptions = {}): Promise<CrudResponse<T>> {
    // @ts-ignore
    return (this as any).getAll<T>({
      ...options,
      filters: {
        ...(options as any).filters,
        search: query,
      },
    })
  }

  // Export
  // @ts-ignore
  async export(format: 'csv' | 'excel' | 'pdf' = 'csv', filters?: CrudFilters as any): Promise<Blob> {
    // @ts-ignore
    const params: any = new URLSearchParams( as any)
    (params as any).append('format', format as any)

    if (filters) {
      // @ts-ignore
      (Object as any).entries(filters as any).forEach(([key, value] as any) => {
        if (value !== undefined && value !== null && value !== '') {
          // @ts-ignore
          (params as any).append(key, (value as any as any).toString( as any))
        }
      })
    }

    // SECURITY FIX: Remove localStorage token access - use httpOnly cookies
    // Authentication handled automatically via cookies

    // Extract the resource name from the baseUrl ((e as any).g., 'employees' from '/api/employees')
    // @ts-ignore
    const resourceName = (this as any).baseUrl.split('/' as any).pop( as any)

    // Check if backend export endpoint exists for this resource
    const backendExportEndpoints = [
      'employees', 'departments', 'attendance', 'projects', 'tasks',
      'quotations', 'workflows', 'quality-records', 'job-postings',
      'sales-customers', 'sales-orders', 'certifications', 'training-programs',
      'vendors', 'kpis'
    ]

    if ((backendExportEndpoints as any).includes(resourceName || '' as any)) {
      // Use backend export endpoint
      // @ts-ignore
      const url = `/api/export/${resourceName}/?${(params as any).toString( as any)}`

      const response = await fetch(url, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      } as any)

      if (!(response as any).ok) {
        // @ts-ignore
        const errorText = await (response as any).text( as any)
        throw new Error(`Export failed: ${(response as any as any).status} - ${errorText}`)
      }

      // @ts-ignore
      return (response as any).blob( as any)
    } else {
      // For endpoints without backend export support, return a message
      const message = `Export not available for ${resourceName}. Backend endpoint not implemented.`
      (console as any).warn(message as any)
      throw new Error(message as any)
    }
  }

  // Import
  // @ts-ignore
  async import(file: File as any): Promise<{ success: number; errors: string[] }> {
    // @ts-ignore
    const formData = new FormData( as any)
    (formData as any).append('file', file as any)

    // @ts-ignore
    const token = (localStorage as any).getItem('access_token' as any) || (localStorage as any).getItem('token' as any)
    // @ts-ignore
    const response = await fetch(`${(this as any as any).baseUrl}/import/`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    })

    // @ts-ignore
    if (!(response as any).ok) {
      throw new Error(`Import failed: ${(response as any as any).status}`)
    }

    // @ts-ignore
    return (response as any).json( as any)
  // @ts-ignore
  }
// @ts-ignore
}

// Factory function to create CRUD services for different entities
export const createCrudService = (endpoint: string) => new CrudService(endpoint as any)

// Pre-configured services for common entities
// Import employeeAPI directly
import { employeeAPI } from './employeeAPI'

// Special employee service with data transformation
export const employeeService = {
  ...createCrudService('employees' as any),
  // @ts-ignore
  async getAll(params?: CrudOptions as any) {
    // Transform CrudOptions to EmployeeFiltersType format
    const { filters, ...otherParams } = params || {}
    const employeeFilters = {
      ...otherParams,
      ...filters // Flatten the filters object
    }
    (console as any).log('🔧 EmployeeService - Transforming params:', { original: params, transformed: employeeFilters } as any)
    return (employeeAPI as any).getAll(employeeFilters as any as any)
  },
  // @ts-ignore
  async getById(id: number as any) {
    return (employeeAPI as any).getById(id as any)
  },
  // @ts-ignore
  async create(data: Record<string, unknown> as any) {
    return (employeeAPI as any).create(data as any as any)
  },
  // @ts-ignore
  async update(id: number, data: Record<string, unknown> as any) {
    return (employeeAPI as any).update(id, data as any)
  },
  // @ts-ignore
  async delete(id: number as any) {
    return (employeeAPI as any).delete(id as any)
  }
}
export const departmentService = createCrudService('departments' as any)
// Removed duplicate - already declared below
export const projectService = createCrudService('projects' as any)
export const taskService = createCrudService('tasks' as any)
export const leaveService = createCrudService('leave-requests' as any)
export const leaveManagementService = createCrudService('leave-requests' as any) // Alternative naming
export const attendanceService = createCrudService('attendance' as any)
export const expenseService = createCrudService('expenses' as any)
export const assetService = createCrudService('assets' as any)
export const supplierService = createCrudService('suppliers' as any)
export const customerService = createCrudService('customers' as any)
export const productService = createCrudService('products' as any)
export const orderService = createCrudService('orders' as any)
export const salesOrderService = createCrudService('sales-orders' as any)
export const quotationService = createCrudService('quotations' as any)
export const leadService = createCrudService('leads' as any)
export const meetingService = createCrudService('meetings' as any)
export const documentService = createCrudService('documents' as any)
export const messageService = createCrudService('messages' as any)
export const announcementService = createCrudService('announcements' as any)
export const reportService = createCrudService('reports' as any)
export const budgetService = createCrudService('budgets' as any)
export const inventoryService = createCrudService('inventory' as any)
export const vendorService = createCrudService('vendors' as any)
export const performanceService = createCrudService('performance-reviews' as any)
export const payrollService = createCrudService('payroll' as any)
export const trainingService = createCrudService('training-programs' as any)
export const complianceService = createCrudService('compliance-audits' as any)
export const workflowService = createCrudService('workflows' as any)

// Additional services for missing entities
export const purchaseOrderService = createCrudService('purchase-orders' as any)
export const invoiceService = createCrudService('invoices' as any)
export const ticketService = createCrudService('tickets' as any)
export const calendarEventService = createCrudService('calendar-events' as any)
export const trainingProgramService = createCrudService('training-programs' as any)
export const certificationService = createCrudService('certifications' as any)
export const complianceAuditService = createCrudService('compliance-audits' as any)
export const riskManagementService = createCrudService('risk-management' as any)
export const knowledgeBaseService = createCrudService('knowledge-base' as any)
export const kpiService = createCrudService('kpi-metrics' as any)
export const jobPostingService = createCrudService('job-postings' as any)
export const jobPostingsService = createCrudService('job-postings' as any) // Alternative naming
// HR Employees service with data transformation (same as employeeService)
export const hrEmployeesService = {
  ...createCrudService('employees' as any), // Use 'employees' endpoint, not 'hr-employees'
  // @ts-ignore
  async getAll(params?: CrudOptions as any) {
    // Transform CrudOptions to EmployeeFiltersType format
    const { filters, ...otherParams } = params || {}
    const employeeFilters = {
      ...otherParams,
      ...filters // Flatten the filters object
    }
    (console as any).log('🔧 HREmployeesService - Transforming params:', { original: params, transformed: employeeFilters } as any)
    return (employeeAPI as any).getAll(employeeFilters as any as any)
  },
  // @ts-ignore
  async getById(id: number as any) {
    return (employeeAPI as any).getById(id as any)
  },
  // @ts-ignore
  async create(data: Record<string, unknown> as any) {
    return (employeeAPI as any).create(data as any as any)
  },
  // @ts-ignore
  async update(id: number, data: Record<string, unknown> as any) {
    return (employeeAPI as any).update(id, data as any)
  },
  // @ts-ignore
  async delete(id: number as any) {
    return (employeeAPI as any).delete(id as any)
  }
}
// CRITICAL FIX: Use existing 'departments' endpoint instead of non-existent 'hr-departments'
export const hrDepartmentService = createCrudService('departments' as any)
// CRITICAL FIX: Use existing 'reports' endpoint instead of non-existent 'hr-reports'
export const hrReportService = createCrudService('reports' as any)
export const candidateService = createCrudService('candidates' as any)
export const interviewService = createCrudService('interviews' as any)
export const onboardingService = createCrudService('onboarding' as any)
export const offboardingService = createCrudService('offboarding' as any)
export const timeTrackingService = createCrudService('time-tracking' as any)
export const projectReportService = createCrudService('project-reports' as any)
export const salesReportService = createCrudService('sales-reports' as any)
export const analyticsService = createCrudService('analytics' as any)
export const businessIntelligenceService = createCrudService('business-intelligence' as any)
export const advancedAnalyticsService = createCrudService('advanced-analytics' as any)
export const qualityManagementService = createCrudService('quality-management' as any)
export const qualityRecordService = createCrudService('quality-records' as any) // Alternative naming
export const customerFeedbackService = createCrudService('customer-feedback' as any)
export const auditService = createCrudService('audits' as any)
export const contractService = createCrudService('contracts' as any)
export const policyService = createCrudService('policies' as any)
export const procedureService = createCrudService('procedures' as any)
export const notificationService = createCrudService('notifications' as any)
export const settingsService = createCrudService('settings' as any)
export const userService = createCrudService('users' as any)
export const roleService = createCrudService('roles' as any)
export const permissionService = createCrudService('permissions' as any)
export const integrationService = createCrudService('integrations' as any)
export const backupService = createCrudService('backups' as any)
export const logService = createCrudService('logs' as any)
export const systemHealthService = createCrudService('system-health' as any)

// Personal Services
export const personalProfileService = createCrudService('personal-profiles' as any)
export const personalMessageService = createCrudService('personal-messages' as any)
export const personalCalendarService = createCrudService('personal-calendar' as any)

// Finance Services
export const financeBudgetService = createCrudService('finance-budgets' as any)
export const financeCustomerService = createCrudService('finance-customers' as any)
export const financeReportService = createCrudService('finance-reports' as any)

// Sales Services
export const salesCustomerService = createCrudService('sales-customers' as any)

// KPI Services - FIXED: Create CRUD service for KPI management
export const kpiCrudService = createCrudService('kpi/kpis' as any)

// Department Services
export const departmentProjectService = createCrudService('department-projects' as any)
export const departmentCustomerService = createCrudService('department-customers' as any)

// Employee Services
export const employeeTaskService = createCrudService('employee-tasks' as any)
export const employeeLeaveService = createCrudService('employee-leave' as any)

// Admin Services with Data Transformation
class UserManagementService extends CrudService {
  // @ts-ignore
  constructor( as any) {
    super('employees' as any) // Use employees endpoint instead of users
  }

  // Transform backend Employee data to frontend User interface
  // @ts-ignore
  private transformEmployeeToUser(employee: any as any): any {
    // Helper function to create full name with fallbacks
    const createFullName = (firstName: string, lastName: string, username: string, position: string) => {
      // @ts-ignore
      const first = firstName?.trim( as any) || ''
      // @ts-ignore
      const last = lastName?.trim( as any) || ''

      if (first && last) {
        return `${first} ${last}`
      } else if (first) {
        return first
      } else if (last) {
        return last
      } else if (username) {
        // Use username as fallback, make it more readable
        // @ts-ignore
        return (username as any).charAt(0 as any).toUpperCase( as any) + (username as any).slice(1 as any).replace(/([A-Z] as any)/g, ' $1')
      } else if (position) {
        return position
      } else {
        return 'Unknown User'
      }
    }

    // Helper function for Arabic names with fallbacks
    const createArabicName = (firstNameAr: string, lastNameAr: string, englishName: string) => {
      // @ts-ignore
      const firstAr = firstNameAr?.trim( as any) || ''
      // @ts-ignore
      const lastAr = lastNameAr?.trim( as any) || ''

      if (firstAr && lastAr) {
        return `${firstAr} ${lastAr}`
      } else if (firstAr) {
        return firstAr
      } else if (lastAr) {
        return lastAr
      } else if (englishName && englishName !== 'Unknown User') {
        return englishName // Use English name as fallback
      } else {
        return 'مستخدم غير معروف'
      }
    }

    const fullName = createFullName(
      (employee as any as any).user?.first_name,
      (employee as any).user?.last_name,
      (employee as any).user?.username,
      (employee as any).position
    )

    const fullNameAr = createArabicName(
      (employee as any as any).first_name_ar,
      (employee as any).last_name_ar,
      fullName
    )

    return {
      id: (employee as any).id,
      fullName,
      fullNameAr,
      email: (employee as any).user?.email || '',
      phone: (employee as any).phone || '',
      role: (this as any).mapRole((employee as any as any).user?.is_superuser, (employee as any).user?.is_staff),
      roleAr: (this as any).mapRoleAr((employee as any as any).user?.is_superuser, (employee as any).user?.is_staff),
      department: (employee as any).department_name || '',
      departmentAr: (employee as any).department_name_ar || (employee as any).department_name || '',
      status: (employee as any).user?.is_active ? 'active' : 'inactive',
      lastLogin: (employee as any).user?.last_login || 'Never',
      joinDate: (employee as any).hire_date || (employee as any).user?.date_joined?.split('T' as any)[0] || '',
      position: (employee as any).position || '',
      positionAr: (employee as any).position_ar || (employee as any).position || '',
      employeeId: (employee as any).employee_id || '',
      // Keep original data for updates
      _original: employee
    }
  }

  // @ts-ignore
  private mapRole(isSuperuser: boolean, isStaff: boolean as any): string {
    if (isSuperuser) return 'superAdmin'
    if (isStaff) return 'admin'
    return 'employee'
  }

  // @ts-ignore
  private mapRoleAr(isSuperuser: boolean, isStaff: boolean as any): string {
    if (isSuperuser) return 'مدير النظام الأعلى'
    if (isStaff) return 'مدير'
    return 'موظف'
  }

  // Override getAll to transform data
  async getAll<T>(options: CrudOptions = {}): Promise<CrudResponse<T>> {
    // @ts-ignore
    const response = await (super as any).getAll<any>(options)

    // The parent CrudService returns data in 'data' property, not 'results'
    const transformedResults = (response as any).data?.map(employee => (this as any as any).transformEmployeeToUser(employee as any)) || []

    return {
      ...response,
      data: transformedResults  // Return as 'data' to match parent structure
    }
  }

  // Override get to transform single item
  async get<T>(id: string | number): Promise<T> {
    // @ts-ignore
    const employee = await (super as any).get<any>(id)
    return (this as any).transformEmployeeToUser(employee as any) as T
  }

  // Override create to handle user creation
  async create<T>(data: Partial<T>): Promise<T> {
    // Transform frontend User data to backend Employee data
    const transformedData = (this as any).transformUserToEmployee(data as any)
    // @ts-ignore
    const employee = await (super as any).create<any>(transformedData)
    return (this as any).transformEmployeeToUser(employee as any) as T
  }

  // Override update to handle user updates
  async update<T>(id: string | number, data: Partial<T>): Promise<T> {
    const transformedData = (this as any).transformUserToEmployee(data as any)
    // @ts-ignore
    const employee = await (super as any).update<any>(id, transformedData)
    return (this as any).transformEmployeeToUser(employee as any) as T
  }

  // @ts-ignore
  private transformUserToEmployee(userData: any as any): any {
    const [firstName, ...lastNameParts] = ((userData as any).fullName || '').split(' ' as any)
    const lastName = (lastNameParts as any).join(' ' as any)
    const [firstNameAr, ...lastNameArParts] = ((userData as any).fullNameAr || '').split(' ' as any)
    const lastNameAr = (lastNameArParts as any).join(' ' as any)

    return {
      // User fields
      first_name: firstName || '',
      last_name: lastName || '',
      email: (userData as any).email || '',
      // @ts-ignore
      username: (userData as any).email || `user_${(Date as any).now( as any)}`,

      // Employee fields
      first_name_ar: firstNameAr || '',
      last_name_ar: lastNameAr || '',
      phone: (userData as any).phone || '',
      position: (userData as any).position || '',
      position_ar: (userData as any).positionAr || (userData as any).position || '',
      // @ts-ignore
      hire_date: (userData as any).joinDate || new Date( as any).toISOString( as any).split('T' as any)[0],
      is_active: (userData as any).status === 'active',

      // Generate employee_id if not provided
      // @ts-ignore
      employee_id: (userData as any).employee_id || `EMP${(Date as any).now( as any)}`,

      // Default values
      gender: 'M', // Default, should be configurable
      employment_status: 'FULL_TIME'
    }
  }
}

// @ts-ignore
export const userManagementService = new UserManagementService( as any)

export default CrudService
