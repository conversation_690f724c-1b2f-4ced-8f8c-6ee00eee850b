import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  FolderOpen,
  Calendar,
  Users,
  Target,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  User,
  DollarSign,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { projectService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface ProjectsProps {
  language: 'ar' | 'en'
}

interface Project {
  id: number
  name: string
  name_ar: string
  description: string
  description_ar?: string
  project_manager: number
  project_manager_name?: string
  department?: number
  department_name?: string
  client?: string
  budget: number
  actual_cost: number
  start_date: string
  end_date: string
  status: 'PLANNING' | 'IN_PROGRESS' | 'ON_HOLD' | 'COMPLETED' | 'CANCELLED'
  priority: 'low' | 'medium' | 'high'
  progress_percentage: number
  is_active: boolean
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    projects: 'المشاريع',
    createProject: 'إنشاء مشروع',
    projectOverview: 'نظرة عامة على المشاريع',
    search: 'بحث',
    filter: 'تصفية',
    projectName: 'اسم المشروع',
    projectManager: 'مدير المشروع',
    department: 'القسم',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    progress: 'التقدم',
    status: 'الحالة',
    budget: 'الميزانية',
    teamMembers: 'أعضاء الفريق',
    actions: 'الإجراءات',
    planning: 'التخطيط',
    inProgress: 'قيد التنفيذ',
    onHold: 'معلق',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    viewDetails: 'عرض التفاصيل',
    editProject: 'تعديل المشروع',
    totalProjects: 'إجمالي المشاريع',
    activeProjects: 'المشاريع النشطة',
    completedProjects: 'المشاريع المكتملة',
    overdueTasks: 'المهام المتأخرة',
    client: 'العميل',
    priority: 'الأولوية',
    low: 'منخفضة',
    medium: 'متوسطة',
    high: 'عالية',
    critical: 'حرجة',
    tasks: 'المهام',
    daysLeft: 'أيام متبقية',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا المشروع؟',
    addProject: 'إضافة مشروع',
    description: 'الوصف',
    actualCost: 'التكلفة الفعلية'
  },
  en: {
    projects: 'Projects',
    createProject: 'Create Project',
    projectOverview: 'Project Overview',
    search: 'Search',
    filter: 'Filter',
    projectName: 'Project Name',
    projectManager: 'Project Manager',
    department: 'Department',
    startDate: 'Start Date',
    endDate: 'End Date',
    progress: 'Progress',
    status: 'Status',
    budget: 'Budget',
    teamMembers: 'Team Members',
    actions: 'Actions',
    planning: 'Planning',
    inProgress: 'In Progress',
    onHold: 'On Hold',
    completed: 'Completed',
    cancelled: 'Cancelled',
    viewDetails: 'View Details',
    editProject: 'Edit Project',
    totalProjects: 'Total Projects',
    activeProjects: 'Active Projects',
    completedProjects: 'Completed Projects',
    overdueTasks: 'Overdue Tasks',
    client: 'Client',
    priority: 'Priority',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical',
    tasks: 'Tasks',
    daysLeft: 'Days Left',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this project?',
    addProject: 'Add Project',
    description: 'Description',
    actualCost: 'Actual Cost'
  }
}

export default function Projects({ language }: ProjectsProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: projects,
    selectedItem,
    loading,
    creating,
    updating,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Project>({
    service: projectService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'PLANNING':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'IN_PROGRESS':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'ON_HOLD':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'COMPLETED':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string): React.ReactElement => {
    switch (status) {
      case 'PLANNING':
        return <Target className="h-3 w-3" />
      case 'IN_PROGRESS':
        return <TrendingUp className="h-3 w-3" />
      case 'ON_HOLD':
        return <Clock className="h-3 w-3" />
      case 'COMPLETED':
        return <CheckCircle className="h-3 w-3" />
      case 'CANCELLED':
        return <AlertTriangle className="h-3 w-3" />
      default:
        return <Clock className="h-3 w-3" />
    }
  }

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'PLANNING': return t.planning
      case 'IN_PROGRESS': return t.inProgress
      case 'ON_HOLD': return t.onHold
      case 'COMPLETED': return t.completed
      case 'CANCELLED': return t.cancelled
      default: return status
    }
  }

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high': return 'text-red-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<Project>[] = [
    {
      key: 'name',
      label: t.projectName,
      sortable: true,
      render: (item: Project) => (
        <div className="flex items-center gap-2">
          <FolderOpen className="h-4 w-4 text-blue-400" />
          <div>
            <span className="text-white font-medium">{item.name}</span>
            {item.client && (
              <div className="text-white/60 text-sm">{item.client}</div>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'project_manager_name',
      label: t.projectManager,
      render: (item: Project) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.project_manager_name || 'N/A'}</span>
        </div>
      )
    },
    {
      key: 'department_name',
      label: t.department,
      render: (item: Project) => (
        <span className="text-white/80">{item.department_name || 'N/A'}</span>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Project) => (
        <Badge className={getStatusColor(item.status)}>
          {getStatusIcon(item.status)}
          <span className="ml-1">{getStatusText(item.status)}</span>
        </Badge>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      render: (item: Project) => (
        <span className={`font-medium ${getPriorityColor(item.priority)}`}>
          {item.priority.charAt(0).toUpperCase() + item.priority.slice(1)}
        </span>
      )
    },
    {
      key: 'progress_percentage',
      label: t.progress,
      sortable: true,
      render: (item: Project) => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-white/20 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full"
              style={{ width: `${item.progress_percentage}%` }}
            ></div>
          </div>
          <span className="text-white/80 text-sm">{item.progress_percentage}%</span>
        </div>
      )
    },
    {
      key: 'budget',
      label: t.budget,
      sortable: true,
      render: (item: Project) => (
        <div className="text-right">
          <div className="text-green-400 font-medium">{formatCurrency(item.budget)}</div>
          {item.actual_cost > 0 && (
            <div className="text-white/60 text-sm">{formatCurrency(item.actual_cost)} spent</div>
          )}
        </div>
      )
    },
    {
      key: 'end_date',
      label: t.endDate,
      sortable: true,
      render: (item: Project) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.end_date}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Project>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Project) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Project) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: Project) => {
        if (confirm(t.confirmDelete)) {
          deleteItem(item.id)
        }
      },
      variant: 'ghost'
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.projectName,
      type: 'text',
      required: true
    },
    {
      name: 'name_ar',
      label: t.projectName + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'description_ar',
      label: t.description + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'project_manager',
      label: t.projectManager,
      type: 'number',
      required: true,
      placeholder: 'Project Manager ID'
    },
    {
      name: 'department',
      label: t.department,
      type: 'number',
      placeholder: 'Department ID'
    },
    {
      name: 'client',
      label: t.client,
      type: 'text'
    },
    {
      name: 'budget',
      label: t.budget,
      type: 'number',
      required: true,
      min: 0,
      step: 0.01
    },
    {
      name: 'actual_cost',
      label: t.actualCost,
      type: 'number',
      min: 0,
      step: 0.01
    },
    {
      name: 'start_date',
      label: t.startDate,
      type: 'date',
      required: true
    },
    {
      name: 'end_date',
      label: t.endDate,
      type: 'date',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.planning, value: 'PLANNING' },
        { label: t.inProgress, value: 'IN_PROGRESS' },
        { label: t.onHold, value: 'ON_HOLD' },
        { label: t.completed, value: 'COMPLETED' },
        { label: t.cancelled, value: 'CANCELLED' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' }
      ]
    },
    {
      name: 'progress_percentage',
      label: t.progress,
      type: 'number',
      min: 0,
      max: 100
    },
    {
      name: 'is_active',
      label: 'Active',
      type: 'checkbox'
    }
  ]

  // Modal handlers
  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Project>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.projects}
        data={projects}
        columns={columns}
        actions={actions}
        loading={loading}
        searchPlaceholder={t.search}
        language={language}
        onCreate={() => {
          setModalMode('create')
          setShowModal(true)
        }}
        onRefresh={refresh}
        onExport={exportData}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText={t.addProject}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addProject : modalMode === 'edit' ? t.editProject : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
    totalProjects: 24,
    activeProjects: 18,
    completedProjects: 6,
    overdueTasks: 12
  }

  const projects = [
    {
      id: 1,
      name: 'نظام إدارة المخزون',
      nameEn: 'Inventory Management System',
      manager: 'أحمد محمد',
      department: 'تقنية المعلومات',
      client: 'شركة التجارة المتقدمة',
      startDate: '2024-01-01',
      endDate: '2024-06-30',
      progress: 75,
      status: 'inProgress',
      priority: 'high',
      budget: 150000,
      teamMembers: 8,
      tasksCount: 45,
      completedTasks: 34
    },
    {
      id: 2,
      name: 'تطوير تطبيق الموبايل',
      nameEn: 'Mobile App Development',
      manager: 'فاطمة علي',
      department: 'تقنية المعلومات',
      client: 'شركة الخدمات الذكية',
      startDate: '2024-02-15',
      endDate: '2024-08-15',
      progress: 45,
      status: 'inProgress',
      priority: 'medium',
      budget: 200000,
      teamMembers: 6,
      tasksCount: 32,
      completedTasks: 14
    },
    {
      id: 3,
      name: 'تحديث نظام المحاسبة',
      nameEn: 'Accounting System Update',
      manager: 'محمد حسن',
      department: 'المالية',
      client: 'داخلي',
      startDate: '2023-10-01',
      endDate: '2024-01-31',
      progress: 100,
      status: 'completed',
      priority: 'high',
      budget: 80000,
      teamMembers: 4,
      tasksCount: 28,
      completedTasks: 28
    },
    {
      id: 4,
      name: 'حملة التسويق الرقمي',
      nameEn: 'Digital Marketing Campaign',
      manager: 'سارة أحمد',
      department: 'التسويق',
      client: 'شركة المنتجات الاستهلاكية',
      startDate: '2024-03-01',
      endDate: '2024-05-31',
      progress: 20,
      status: 'planning',
      priority: 'medium',
      budget: 50000,
      teamMembers: 5,
      tasksCount: 18,
      completedTasks: 3
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'planning':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'inProgress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'onHold':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'text-green-400'
      case 'medium':
        return 'text-yellow-400'
      case 'high':
        return 'text-orange-400'
      case 'critical':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'planning':
        return <Target className="h-4 w-4 text-gray-500" />
      case 'inProgress':
        return <Clock className="h-4 w-4 text-blue-500" />
      case 'onHold':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const calculateDaysLeft = (endDate: string) => {
    const end = new Date(endDate)
    const today = new Date()
    const diffTime = end.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.projects}</h1>
          <p className="text-white/70">إدارة ومتابعة المشاريع والمهام</p>
        </div>
        <Button className="glass-button">
          <Plus className="h-4 w-4 mr-2" />
          {t.createProject}
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalProjects}</p>
                <p className="text-2xl font-bold text-white">{projectStats.totalProjects}</p>
              </div>
              <FolderOpen className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.activeProjects}</p>
                <p className="text-2xl font-bold text-blue-400">{projectStats.activeProjects}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.completedProjects}</p>
                <p className="text-2xl font-bold text-green-400">{projectStats.completedProjects}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.overdueTasks}</p>
                <p className="text-2xl font-bold text-red-400">{projectStats.overdueTasks}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Projects List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">{t.projectOverview}</CardTitle>
              <CardDescription className="text-white/70">
                قائمة بجميع المشاريع وحالتها الحالية
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {projects.map((project) => (
              <Card key={project.id} className="glass-card border-white/10 hover:border-white/30 transition-all">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <CardTitle className="text-white text-lg mb-1">{project.name}</CardTitle>
                      <CardDescription className="text-white/70 text-sm">
                        {project.client} • {project.department}
                      </CardDescription>
                    </div>
                    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(project.status)}`}>
                      {getStatusIcon(project.status)}
                      {t[project.status as keyof typeof t]}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Project Manager and Team */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-blue-400" />
                      <span className="text-white/80">{project.manager}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-green-400" />
                      <span className="text-white/80">{project.teamMembers} أعضاء</span>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-white/70">{t.progress}</span>
                      <span className="text-white font-medium">{project.progress}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all"
                        style={{ width: `${project.progress}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Tasks and Budget */}
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-white/70">{t.tasks}</p>
                      <p className="text-white font-medium">{project.completedTasks}/{project.tasksCount}</p>
                    </div>
                    <div>
                      <p className="text-white/70">{t.budget}</p>
                      <p className="text-white font-medium">{formatCurrency(project.budget)}</p>
                    </div>
                  </div>

                  {/* Timeline */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-purple-400" />
                      <span className="text-white/80">{project.startDate} - {project.endDate}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4 text-orange-400" />
                      <span className="text-orange-400">{calculateDaysLeft(project.endDate)} {t.daysLeft}</span>
                    </div>
                  </div>

                  {/* Priority */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-white/70 text-sm">{t.priority}:</span>
                      <span className={`text-sm font-medium ${getPriorityColor(project.priority)}`}>
                        {t[project.priority as keyof typeof t]}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" className="glass-button">
                        {t.viewDetails}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
