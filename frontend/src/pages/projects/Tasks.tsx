import { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  CheckSquare,
  Calendar,
  User,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  BarChart3,
  Flag,
  FolderOpen,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { taskService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface TasksProps {
  language: 'ar' | 'en'
}

interface Task {
  id: number
  project: number
  project_name?: string
  title: string
  title_ar?: string
  description: string
  description_ar?: string
  assigned_to?: number
  assigned_to_name?: string
  created_by: number
  created_by_name?: string
  due_date: string
  estimated_hours: number
  actual_hours: number
  status: 'TODO' | 'IN_PROGRESS' | 'REVIEW' | 'COMPLETED' | 'CANCELLED'
  priority: 'low' | 'medium' | 'high'
  completion_percentage: number
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    tasks: 'المهام',
    createTask: 'إنشاء مهمة',
    taskOverview: 'نظرة عامة على المهام',
    search: 'بحث',
    filter: 'تصفية',
    taskTitle: 'عنوان المهمة',
    project: 'المشروع',
    assignedTo: 'مكلف إلى',
    dueDate: 'تاريخ الاستحقاق',
    priority: 'الأولوية',
    status: 'الحالة',
    progress: 'التقدم',
    actions: 'الإجراءات',
    todo: 'للقيام',
    inProgress: 'قيد التنفيذ',
    review: 'مراجعة',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    low: 'منخفضة',
    medium: 'متوسطة',
    high: 'عالية',
    urgent: 'عاجل',
    viewDetails: 'عرض التفاصيل',
    editTask: 'تعديل المهمة',
    totalTasks: 'إجمالي المهام',
    completedTasks: 'المهام المكتملة',
    inProgressTasks: 'المهام قيد التنفيذ',
    overdueTasks: 'المهام المتأخرة',
    estimatedHours: 'الساعات المقدرة',
    actualHours: 'الساعات الفعلية',
    description: 'الوصف',
    dependencies: 'التبعيات',
    tags: 'العلامات',
    createdBy: 'أنشأ بواسطة',
    startDate: 'تاريخ البداية',
    completionDate: 'تاريخ الإكمال',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه المهمة؟',
    addTask: 'إضافة مهمة'
  },
  en: {
    tasks: 'Tasks',
    createTask: 'Create Task',
    taskOverview: 'Task Overview',
    search: 'Search',
    filter: 'Filter',
    taskTitle: 'Task Title',
    project: 'Project',
    assignedTo: 'Assigned To',
    dueDate: 'Due Date',
    priority: 'Priority',
    status: 'Status',
    progress: 'Progress',
    actions: 'Actions',
    todo: 'To Do',
    inProgress: 'In Progress',
    review: 'Review',
    completed: 'Completed',
    cancelled: 'Cancelled',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    urgent: 'Urgent',
    viewDetails: 'View Details',
    editTask: 'Edit Task',
    totalTasks: 'Total Tasks',
    completedTasks: 'Completed Tasks',
    inProgressTasks: 'In Progress Tasks',
    overdueTasks: 'Overdue Tasks',
    estimatedHours: 'Estimated Hours',
    actualHours: 'Actual Hours',
    description: 'Description',
    dependencies: 'Dependencies',
    tags: 'Tags',
    createdBy: 'Created By',
    startDate: 'Start Date',
    completionDate: 'Completion Date',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this task?',
    addTask: 'Add Task'
  }
}

export default function Tasks({ language }: TasksProps) {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: tasks,
    selectedItem,
    loading,
    creating,
    updating,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Task>({
    service: taskService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'TODO':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'REVIEW':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string): React.ReactElement => {
    switch (status) {
      case 'TODO':
        return <CheckSquare className="h-3 w-3" />
      case 'IN_PROGRESS':
        return <BarChart3 className="h-3 w-3" />
      case 'REVIEW':
        return <AlertTriangle className="h-3 w-3" />
      case 'COMPLETED':
        return <CheckCircle className="h-3 w-3" />
      case 'CANCELLED':
        return <XCircle className="h-3 w-3" />
      default:
        return <CheckSquare className="h-3 w-3" />
    }
  }

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'TODO': return t.todo
      case 'IN_PROGRESS': return t.inProgress
      case 'REVIEW': return t.review
      case 'COMPLETED': return t.completed
      case 'CANCELLED': return t.cancelled
      default: return status
    }
  }

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high': return 'text-red-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  const getPriorityIcon = (priority: string): React.ReactElement => {
    return <Flag className={`h-3 w-3 ${getPriorityColor(priority)}`} />
  }

  // Table columns configuration
  const columns: TableColumn<Task>[] = [
    {
      key: 'title',
      label: t.taskTitle,
      sortable: true,
      render: (item: Task) => (
        <div className="flex items-center gap-2">
          <CheckSquare className="h-4 w-4 text-blue-400" />
          <div>
            <span className="text-white font-medium">{item.title}</span>
            {item.project_name && (
              <div className="text-white/60 text-sm flex items-center gap-1">
                <FolderOpen className="h-3 w-3" />
                {item.project_name}
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'assigned_to_name',
      label: t.assignedTo,
      render: (item: Task) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.assigned_to_name || 'Unassigned'}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Task) => (
        <Badge className={getStatusColor(item.status)}>
          {getStatusIcon(item.status)}
          <span className="ml-1">{getStatusText(item.status)}</span>
        </Badge>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: Task) => (
        <div className="flex items-center gap-1">
          {getPriorityIcon(item.priority)}
          <span className={`font-medium ${getPriorityColor(item.priority)}`}>
            {item.priority.charAt(0).toUpperCase() + item.priority.slice(1)}
          </span>
        </div>
      )
    },
    {
      key: 'due_date',
      label: t.dueDate,
      sortable: true,
      render: (item: Task) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.due_date}</span>
        </div>
      )
    },
    {
      key: 'completion_percentage',
      label: t.progress,
      sortable: true,
      render: (item: Task) => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-white/20 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full"
              style={{ width: `${item.completion_percentage}%` }}
            ></div>
          </div>
          <span className="text-white/80 text-sm">{item.completion_percentage}%</span>
        </div>
      )
    },
    {
      key: 'estimated_hours',
      label: t.estimatedHours,
      render: (item: Task) => (
        <div className="text-right">
          <div className="text-blue-400 font-medium">{item.estimated_hours}h</div>
          {item.actual_hours > 0 && (
            <div className="text-white/60 text-sm">{item.actual_hours}h actual</div>
          )}
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Task>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Task) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Task) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: Task) => {
        if (confirm(t.confirmDelete)) {
          deleteItem(item.id)
        }
      },
      variant: 'ghost'
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'project',
      label: t.project,
      type: 'number',
      required: true,
      placeholder: 'Project ID'
    },
    {
      name: 'title',
      label: t.taskTitle,
      type: 'text',
      required: true
    },
    {
      name: 'title_ar',
      label: t.taskTitle + ' (عربي)',
      type: 'text'
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'description_ar',
      label: t.description + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'assigned_to',
      label: t.assignedTo,
      type: 'number',
      placeholder: 'Employee ID'
    },
    {
      name: 'created_by',
      label: t.createdBy,
      type: 'number',
      required: true,
      placeholder: 'Creator Employee ID'
    },
    {
      name: 'due_date',
      label: t.dueDate,
      type: 'date',
      required: true
    },
    {
      name: 'estimated_hours',
      label: t.estimatedHours,
      type: 'number',
      min: 0,
      step: 0.25
    },
    {
      name: 'actual_hours',
      label: t.actualHours,
      type: 'number',
      min: 0,
      step: 0.25
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.todo, value: 'TODO' },
        { label: t.inProgress, value: 'IN_PROGRESS' },
        { label: t.review, value: 'REVIEW' },
        { label: t.completed, value: 'COMPLETED' },
        { label: t.cancelled, value: 'CANCELLED' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' }
      ]
    },
    {
      name: 'completion_percentage',
      label: t.progress,
      type: 'number',
      min: 0,
      max: 100
    }
  ]

  // Modal handlers
  const handleModalClose = () => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Task>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.tasks}
        data={tasks}
        columns={columns}
        actions={actions}
        loading={loading}
        searchPlaceholder={t.search}
        language={language}
        onCreate={() => {
          setModalMode('create')
          setShowModal(true)
        }}
        onRefresh={refresh}
        onExport={exportData}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText={t.addTask}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addTask : modalMode === 'edit' ? t.editTask : t.view}
        fields={formFields}
        initialData={selectedItem}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
    totalTasks: 156,
    completedTasks: 89,
    inProgressTasks: 45,
    overdueTasks: 22
  }

  const tasks = [
    {
      id: 1,
      title: 'تصميم واجهة المستخدم الرئيسية',
      project: 'نظام إدارة المخزون',
      assignedTo: 'أحمد محمد',
      createdBy: 'فاطمة علي',
      dueDate: '2024-02-15',
      startDate: '2024-01-20',
      priority: 'high',
      status: 'inProgress',
      progress: 65,
      estimatedHours: 40,
      actualHours: 26,
      description: 'تصميم وتطوير واجهة المستخدم الرئيسية للنظام',
      tags: ['UI/UX', 'Frontend', 'Design']
    },
    {
      id: 2,
      title: 'إعداد قاعدة البيانات',
      project: 'تطوير تطبيق الموبايل',
      assignedTo: 'محمد حسن',
      createdBy: 'أحمد محمد',
      dueDate: '2024-02-10',
      startDate: '2024-01-25',
      priority: 'urgent',
      status: 'review',
      progress: 90,
      estimatedHours: 24,
      actualHours: 22,
      description: 'إنشاء وإعداد قاعدة البيانات للتطبيق',
      tags: ['Database', 'Backend', 'Setup']
    },
    {
      id: 3,
      title: 'اختبار وحدة المدفوعات',
      project: 'تحديث نظام المحاسبة',
      assignedTo: 'سارة أحمد',
      createdBy: 'محمد حسن',
      dueDate: '2024-01-30',
      startDate: '2024-01-15',
      priority: 'medium',
      status: 'completed',
      progress: 100,
      estimatedHours: 16,
      actualHours: 18,
      description: 'اختبار شامل لوحدة المدفوعات والتحقق من صحة العمليات',
      tags: ['Testing', 'Payments', 'QA']
    },
    {
      id: 4,
      title: 'كتابة الوثائق التقنية',
      project: 'حملة التسويق الرقمي',
      assignedTo: 'علي محمود',
      createdBy: 'سارة أحمد',
      dueDate: '2024-02-20',
      startDate: '2024-02-01',
      priority: 'low',
      status: 'todo',
      progress: 0,
      estimatedHours: 12,
      actualHours: 0,
      description: 'إعداد الوثائق التقنية للحملة التسويقية',
      tags: ['Documentation', 'Marketing', 'Content']
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'todo':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'inProgress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low':
        return 'text-green-400'
      case 'medium':
        return 'text-yellow-400'
      case 'high':
        return 'text-orange-400'
      case 'urgent':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'todo':
        return <CheckSquare className="h-4 w-4 text-gray-500" />
      case 'inProgress':
        return <Clock className="h-4 w-4 text-blue-500" />
      case 'review':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const isOverdue = (dueDate: string, status: string) => {
    if (status === 'completed') return false
    const due = new Date(dueDate)
    const today = new Date()
    return due < today
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.tasks}</h1>
          <p className="text-white/70">إدارة ومتابعة المهام والأنشطة</p>
        </div>
        <div className="flex gap-2">
          <Button className="glass-button">
            <Plus className="h-4 w-4 mr-2" />
            {t.createTask}
          </Button>
          <Button 
            variant="outline" 
            className="glass-button"
            onClick={() => setViewMode(viewMode === 'list' ? 'kanban' : 'list')}
          >
            <BarChart3 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.totalTasks}</p>
                <p className="text-2xl font-bold text-white">{taskStats.totalTasks}</p>
              </div>
              <CheckSquare className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.completedTasks}</p>
                <p className="text-2xl font-bold text-green-400">{taskStats.completedTasks}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.inProgressTasks}</p>
                <p className="text-2xl font-bold text-blue-400">{taskStats.inProgressTasks}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.overdueTasks}</p>
                <p className="text-2xl font-bold text-red-400">{taskStats.overdueTasks}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tasks List */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle className="text-white text-xl">{t.taskOverview}</CardTitle>
              <CardDescription className="text-white/70">
                قائمة بجميع المهام وحالتها الحالية
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 h-4 w-4" />
                <Input
                  placeholder={t.search}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 glass-input"
                />
              </div>
              <Button variant="outline" className="glass-button">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {tasks.map((task) => (
              <Card key={task.id} className={`glass-card border-white/10 hover:border-white/30 transition-all ${isOverdue(task.dueDate, task.status) ? 'border-red-400/50' : ''}`}>
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                    {/* Task Info */}
                    <div className="flex-1 space-y-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-white font-semibold text-lg mb-1">{task.title}</h3>
                          <div className="flex items-center gap-4 text-sm text-white/70">
                            <div className="flex items-center gap-1">
                              <FolderOpen className="h-4 w-4 text-blue-400" />
                              <span>{task.project}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <User className="h-4 w-4 text-green-400" />
                              <span>{task.assignedTo}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status)}`}>
                            {getStatusIcon(task.status)}
                            {t[task.status as keyof typeof t]}
                          </div>
                          <Flag className={`h-4 w-4 ${getPriorityColor(task.priority)}`} />
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-white/70">{t.progress}</span>
                          <span className="text-white">{task.progress}%</span>
                        </div>
                        <div className="w-full bg-white/20 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all"
                            style={{ width: `${task.progress}%` }}
                          ></div>
                        </div>
                      </div>

                      {/* Task Details */}
                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-white/70">{t.dueDate}</p>
                          <p className={`font-medium ${isOverdue(task.dueDate, task.status) ? 'text-red-400' : 'text-white'}`}>
                            {task.dueDate}
                          </p>
                        </div>
                        <div>
                          <p className="text-white/70">{t.estimatedHours}</p>
                          <p className="text-white font-medium">{task.estimatedHours}h</p>
                        </div>
                        <div>
                          <p className="text-white/70">{t.actualHours}</p>
                          <p className="text-white font-medium">{task.actualHours}h</p>
                        </div>
                        <div>
                          <p className="text-white/70">{t.priority}</p>
                          <p className={`font-medium ${getPriorityColor(task.priority)}`}>
                            {t[task.priority as keyof typeof t]}
                          </p>
                        </div>
                      </div>

                      {/* Tags */}
                      <div className="flex flex-wrap gap-2">
                        {task.tags.map((tag, index) => (
                          <span key={index} className="px-2 py-1 bg-white/10 text-white/80 text-xs rounded-full">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button size="sm" className="glass-button">
                        {t.viewDetails}
                      </Button>
                      <Button size="sm" variant="outline" className="glass-button">
                        {t.editTask}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
