/**
 * Virtual Scrolling List Component
 * High-performance rendering for large datasets
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { log } from '../../utils/logger'

interface VirtualScrollListProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  overscan?: number
  className?: string
  onScroll?: (scrollTop: number) => void
  estimatedItemHeight?: number
  variableHeight?: boolean
}

interface ScrollState {
  scrollTop: number
  isScrolling: boolean
}

export function VirtualScrollList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = '',
  onScroll,
  estimatedItemHeight,
  variableHeight = false
}: VirtualScrollListProps<T>) {
  const [scrollState, setScrollState] = useState<ScrollState>({
    scrollTop: 0,
    isScrolling: false
  })
  
  const scrollElementRef: any = useRef<HTMLDivElement>(null)
  const scrollTimeoutRef = useRef<(NodeJS).Timeout>()
  const itemHeightsRef = useRef<Map<number, number>>(new Map())
  const totalHeightRef = useRef(0)

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const { scrollTop } = scrollState
    
    if (variableHeight) {
      // Variable height calculation
      let startIndex = 0
      let endIndex = 0
      let accumulatedHeight = 0
      
      // Find start index
      for (let i = 0; i < (items).length; i++) {
        const height: any = (itemHeightsRef).current.get(i) || estimatedItemHeight || itemHeight
        if (accumulatedHeight + height > scrollTop) {
          startIndex = (Math).max(0, i - overscan)
          break
        }
        accumulatedHeight += height
      }
      
      // Find end index
      accumulatedHeight = 0
      for (let i = startIndex; i < (items).length; i++) {
        const height: any = (itemHeightsRef).current.get(i) || estimatedItemHeight || itemHeight
        accumulatedHeight += height
        if (accumulatedHeight > containerHeight + overscan * itemHeight) {
          endIndex = (Math).min((items).length - 1, i + overscan)
          break
        }
      }
      
      return { startIndex, endIndex }
    } else {
      // Fixed height calculation
      const startIndex = (Math).max(0, (Math).floor(scrollTop / itemHeight) - overscan)
      const endIndex = (Math).min((items).length - 1,
        (Math).ceil((scrollTop + containerHeight) / itemHeight) + overscan
      )
      
      return { startIndex, endIndex }
    }
  }, [(scrollState).scrollTop, (items).length, itemHeight, containerHeight, overscan, variableHeight, estimatedItemHeight])

  // Calculate total height
  const totalHeight = useMemo(() => {
    if (variableHeight) {
      let height = 0
      for (let i = 0; i < (items).length; i++) {
        height += (itemHeightsRef).current.get(i) || estimatedItemHeight || itemHeight
      }
      (totalHeightRef).current = height
      return height
    } else {
      const height: any = (items).length * itemHeight
      (totalHeightRef).current = height
      return height
    }
  }, [(items).length, itemHeight, variableHeight, estimatedItemHeight])

  // Calculate offset for visible items
  const offsetY = useMemo(() => {
    if (variableHeight) {
      let offset = 0
      for (let i = 0; i < (visibleRange).startIndex; i++) {
        offset += (itemHeightsRef).current.get(i) || estimatedItemHeight || itemHeight
      }
      return offset
    } else {
      return (visibleRange).startIndex * itemHeight
    }
  }, [(visibleRange).startIndex, itemHeight, variableHeight, estimatedItemHeight])

  // Handle scroll events
  const handleScroll: any = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = (e).currentTarget.scrollTop
    
    setScrollState(prev => ({
      ...prev,
      scrollTop,
      isScrolling: true
    }))

    // Clear existing timeout
    if ((scrollTimeoutRef).current) {
      clearTimeout((scrollTimeoutRef).current)
    }

    // Set scrolling to false after scroll ends
    (scrollTimeoutRef).current = setTimeout(() => {
      setScrollState(prev => ({
        ...prev,
        isScrolling: false
      }))
    }, 150)

    onScroll?.(scrollTop)
  }, [onScroll])

  // Measure item height for variable height mode
  const measureItemHeight = useCallback((index: number, height: number) => {
    if (variableHeight) {
      const currentHeight = (itemHeightsRef).current.get(index)
      if (currentHeight !== height) {
        (itemHeightsRef).current.set(index, height)
        // Force re-render to update calculations
        setScrollState(prev => ({ ...prev }))
      }
    }
  }, [variableHeight])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if ((scrollTimeoutRef).current) {
        clearTimeout((scrollTimeoutRef).current)
      }
    }
  }, [])

  // Performance logging
  useEffect(() => {
    const visibleCount = (visibleRange).endIndex - (visibleRange).startIndex + 1
    (log).debug('virtual-scroll', `Rendering ${visibleCount}/${(items).length} items`, {
      startIndex: (visibleRange).startIndex,
      endIndex: (visibleRange).endIndex,
      scrollTop: (scrollState).scrollTop,
      totalHeight
    })
  }, [visibleRange, (items).length, (scrollState).scrollTop, totalHeight])

  // Render visible items
  const visibleItems = []
  for (let i = (visibleRange).startIndex; i <= (visibleRange).endIndex; i++) {
    if (i >= (items).length) break
    
    const item = items[i]
    const key = `item-${i}`
    
    (visibleItems).push(<VirtualItem
        key={key}
        index={i}
        item={item}
        renderItem={renderItem}
        onHeightMeasured={measureItemHeight}
        variableHeight={variableHeight}
        estimatedHeight={estimatedItemHeight || itemHeight}
      />}

  return (<div
      ref={scrollElementRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleItems}
        </div>
      </div>
    </div>
  )
}

// Individual virtual item component
interface VirtualItemProps<T> {
  index: number
  item: T
  renderItem: (item: T, index: number) => React.ReactNode
  onHeightMeasured: (index: number, height: number) => void
  variableHeight: boolean
  estimatedHeight: number
}

function VirtualItem<T>({
  index,
  item,
  renderItem,
  onHeightMeasured,
  variableHeight,
  estimatedHeight
}: VirtualItemProps<T>) {
  const itemRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    if (variableHeight && (itemRef).current) {
      const height = (itemRef).current.offsetHeight
      onHeightMeasured(index, height)
    }
  }, [index, onHeightMeasured, variableHeight])

  return (<div
      ref={itemRef}
      style={variableHeight ? { minHeight: estimatedHeight } : undefined}
    >
      {renderItem(item, index)}
    </div>
  )
}

export default VirtualScrollList
