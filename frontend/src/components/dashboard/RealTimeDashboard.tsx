import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Activity,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Bell,
  // Calendar, // TODO: Add calendar integration
  BarChart3,
  type LucideIcon
} from 'lucide-react'
import { useDashboardSync } from '../../hooks/useDashboardSync'

interface RealTimeDashboardProps {
  language: 'ar' | 'en'
  userRole: string
}

interface RealTimeMetric {
  id: string
  title: string
  value: string | number
  change: number
  trend: 'up' | 'down' | 'stable'
  icon: LucideIcon
  color: string
  lastUpdated: Date
}

const translations = {
  ar: {
    realTimeMetrics: 'المؤشرات المباشرة',
    lastUpdated: 'آخر تحديث',
    autoRefresh: 'تحديث تلقائي',
    refreshNow: 'تحديث الآن',
    activeUsers: 'المستخدمون النشطون',
    systemLoad: 'حمولة النظام',
    pendingTasks: 'المهام المعلقة',
    todayRevenue: 'إيرادات اليوم',
    systemAlerts: 'تنبيهات النظام',
    completedToday: 'مكتمل اليوم',
    onlineEmployees: 'الموظفون المتصلون',
    criticalIssues: 'مشاكل حرجة'
  },
  en: {
    realTimeMetrics: 'Real-Time Metrics',
    lastUpdated: 'Last Updated',
    autoRefresh: 'Auto Refresh',
    refreshNow: 'Refresh Now',
    activeUsers: 'Active Users',
    systemLoad: 'System Load',
    pendingTasks: 'Pending Tasks',
    todayRevenue: 'Today Revenue',
    systemAlerts: 'System Alerts',
    completedToday: 'Completed Today',
    onlineEmployees: 'Online Employees',
    criticalIssues: 'Critical Issues'
  }
}
export default function RealTimeDashboard({ language, userRole }: RealTimeDashboardProps): React.ReactElement {
  const [metrics, setMetrics] = useState<RealTimeMetric[]>([])
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [lastUpdate, setLastUpdate] = useState(new Date())
  const [isRefreshing, setIsRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // FIXED: Use dashboard sync hook for data invalidation
  const { refreshDashboardMetrics } = useDashboardSync()

  // Initialize metrics based on user role with real API data
  useEffect(() => {
    const loadRealTimeMetrics = async () => {
      try {
        // Import dashboard API
        const { dashboardAPI } = await import('../../services/api')
        const dashboardStats = await (dashboardAPI).getStats()

        const baseMetrics: RealTimeMetric[] = [
          {
            id: 'activeUsers',
            title: (t).activeUsers,
            value: (dashboardStats).total_employees || 0,
            change: (dashboardStats).employee_growth_rate || 0, // Use real growth rate from API
            trend: ((dashboardStats).employee_growth_rate || 0) > 0 ? 'up' : ((dashboardStats).employee_growth_rate || 0) < 0 ? 'down' : 'stable',
            icon: Users,
            color: 'from-blue-500 to-blue-600',
            lastUpdated: new Date()
          },
          {
            id: 'systemLoad',
            title: (t).systemLoad,
            value: (dashboardStats).system_health?.cpu_usage ? `${(Math).round((dashboardStats).system_health.cpu_usage)}%` : '0%',
            change: (dashboardStats).system_health?.cpu_change || 0, // Use real CPU change from API
            trend: ((dashboardStats).system_health?.cpu_change || 0) > 0 ? 'up' : ((dashboardStats).system_health?.cpu_change || 0) < 0 ? 'down' : 'stable',
            icon: Activity,
            color: 'from-green-500 to-green-600',
            lastUpdated: new Date()
          },
          {
            id: 'pendingTasks',
            title: (t).pendingTasks,
            value: (dashboardStats).pending_tasks || 0,
            change: 0, // TODO: Calculate real change from historical data
            trend: 'stable',
            icon: Clock,
            color: 'from-orange-500 to-orange-600',
            lastUpdated: new Date()
          },
          {
            id: 'todayRevenue',
            title: (t).todayRevenue,
            value: (dashboardStats).monthly_expenses ? `$${(dashboardStats).monthly_expenses.toLocaleString()}` : '$0',
            change: 0, // TODO: Calculate real change from historical data
            trend: 'stable',
            icon: DollarSign,
            color: 'from-purple-500 to-purple-600',
            lastUpdated: new Date()
          }
        ]

        // Add role-specific metrics
        if (userRole === 'super_admin') {
          (baseMetrics).push({
              id: 'systemAlerts',
              title: (t).systemAlerts,
              value: (dashboardStats).system_alerts_count || 0, // Use real system alerts from API
              change: (dashboardStats).alerts_change || 0,
              trend: ((dashboardStats).alerts_change || 0) > 0 ? 'up' : ((dashboardStats).alerts_change || 0) < 0 ? 'down' : 'stable',
              icon: AlertTriangle,
              color: 'from-red-500 to-red-600',
              lastUpdated: new Date()
            },
            {
              id: 'completedToday',
              title: (t).completedToday,
              value: (dashboardStats).active_projects || 0,
              change: (dashboardStats).projects_completion_rate || 0, // Use real completion rate from API
              trend: ((dashboardStats).projects_completion_rate || 0) > 0 ? 'up' : ((dashboardStats).projects_completion_rate || 0) < 0 ? 'down' : 'stable',
              icon: CheckCircle,
              color: 'from-emerald-500 to-emerald-600',
              lastUpdated: new Date()
            }
          )
        }

        setMetrics(baseMetrics)
      } catch (error) {
        console.error('Error loading real-time metrics:', error)

        // Fallback to basic metrics if API fails
        const fallbackMetrics: RealTimeMetric[] = [
          {
            id: 'activeUsers',
            title: (t).activeUsers,
            value: 0,
            change: 0,
            trend: 'stable',
            icon: Users,
            color: 'from-blue-500 to-blue-600',
            lastUpdated: new Date()
          }
        ]
        setMetrics(fallbackMetrics)
      }
    }
    loadRealTimeMetrics()
  }, [userRole, t])

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return
    const interval = setInterval(() => {
      updateMetrics()
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [autoRefresh])

  // Update metrics with real API data
  const updateMetrics = async () => {
    setIsRefreshing(true)

    try {
      // Fetch fresh data from API
      const { dashboardAPI } = await import('../../services/api')
      const dashboardStats = await (dashboardAPI).getStats()

      // Create stable timestamp to prevent cascading re-renders
      const updateTimestamp = new Date()

      setMetrics(prev => (prev).map(metric => {
        let newValue = (metric).value
        let newChange = (metric).change

        // Update with real data based on metric type
        switch ((metric).id) {
          case 'activeUsers':
            newValue = (dashboardStats).total_employees || 0
            break
          case 'pendingTasks':
            newValue = (dashboardStats).pending_tasks || 0
            break
          case 'completedToday':
            newValue = (dashboardStats).active_projects || 0
            break
          // For metrics without direct API equivalents, keep current value
          default:
            break
        }

        return {
          ...metric,
          value: newValue,
          change: newChange,
          lastUpdated: updateTimestamp
        }
      }))

      setLastUpdate(updateTimestamp)
    } catch (error) {
      console.error('Error updating metrics:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const getTrendIcon = (trend: string): void => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'down': return <TrendingDown className="h-4 w-4 text-red-400" />
      default: return <BarChart3 className="h-4 w-4 text-gray-400" />
    }
  }

  const getTrendColor = (trend: string): void => {
    switch (trend) {
      case 'up': return 'text-green-400'
      case 'down': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  return (<Card className="glass-card border-white/20">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white text-xl flex items-center gap-2">
            <Activity className="h-5 w-5" />
            {(t).realTimeMetrics}
          </CardTitle>
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={`glass-button text-xs ${autoRefresh ? 'bg-green-500/20' : 'bg-gray-500/20'}`}
              >
                <Bell className="h-3 w-3 mr-1" />
                {(t).autoRefresh}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={updateMetrics}
                disabled={isRefreshing}
                className="glass-button text-xs"
              >
                <RefreshCw className={`h-3 w-3 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                {(t).refreshNow}
              </Button>
            </div>
          </div>
        </div>
        <p className="text-white/60 text-sm">
          {(t).lastUpdated}: {(lastUpdate).toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US')}
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {(metrics).map((metric) => (<div
              key={(metric).id}
              className="p-4 glass-card border-white/10 hover:border-white/30 transition-all duration-300 group"
            >
              <div className="flex items-center justify-between mb-3">
                <div className={`p-2 rounded-lg bg-gradient-to-r ${(metric).color} group-hover:scale-110 transition-transform duration-300`}>
                  <(metric).icon className="h-4 w-4 text-white" />
                </div>
                <div className="flex items-center gap-1">
                  {getTrendIcon((metric).trend)}
                  <span className={`text-xs font-medium ${getTrendColor((metric).trend)}`}>
                    {(metric).change > 0 ? '+' : ''}{(metric).change.toFixed(1)}%
                  </span>
                </div>
              </div>
              <div>
                <p className="text-white/70 text-xs mb-1">{(metric).title}</p>
                <p className="text-white text-lg font-bold">{(metric).value}</p>
                <p className="text-white/50 text-xs">
                  {(metric).lastUpdated.toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US')}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
