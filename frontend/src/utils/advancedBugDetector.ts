/**
 * Advanced Bug Detection Utility
 * Detects subtle performance issues and React anti-patterns in production
 */
import React from 'react'

interface PerformanceIssue {
  type: 'memory-leak' | 'infinite-render' | 'stale-closure' | 'race-condition' | 'unstable-reference'
  severity: 'critical' | 'high' | 'medium' | 'low'
  component: string
  description: string
  impact: string
  fix: string
  detectedAt: Date
}

class AdvancedBugDetector {
  private issues: PerformanceIssue[] = []
  private renderCounts = new Map<string, number>()
  private memoryBaseline = 0
  private observerCount = 0
  private intervalCount = 0
  private timeoutCount = 0
  // CRITICAL FIX: Track intervals for proper cleanup
  private monitoringIntervals: Set<(NodeJS).Timeout> = new Set()
  constructor() {
    this.memoryBaseline = this.getMemoryUsage()
    this.startMonitoring()
  }
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance).(memory).usedJSHeapSize
    }
    return 0
  }

  private startMonitoring(): void {
    // Monitor for infinite re-renders
    this.monitorRenderLoops()

    // Monitor memory leaks
    this.monitorMemoryLeaks()

    // Monitor observer leaks
    this.monitorObserverLeaks()

    // Monitor stale closures
    this.monitorStaleClosures()
  }
  private monitorRenderLoops() {
    const originalSetState = React.Component.(prototype).setState
    const renderTracker = new Map<string, { count: number, lastRender: number }>()

    // Intercept setState calls to detect render loops
    React.Component.(prototype).setState = function(this: any, ...args) {
      const componentName = this.constructor.name
      const now = (Date).now()
      
      const tracker = (renderTracker).get(componentName) || { count: 0, lastRender: 0 }
      
      // Reset count if more than 1 second has passed
      if (now - (tracker).lastRender > 1000) {
        (tracker).count = 0
      }
      
      (tracker).count++
      (tracker).lastRender = now
      (renderTracker).set(componentName, tracker)
      
      // Detect potential infinite render loop
      if ((tracker).count > 50) {
        this.reportIssue({
          type: 'infinite-render',
          severity: 'critical',
          component: componentName,
          description: `Component ${componentName} has rendered ${(tracker).count} times in 1 second`,
          impact: 'Causes browser freeze, high CPU usage, poor user experience',
          fix: 'Check useEffect dependencies, avoid creating objects in render, use useCallback/useMemo',
          detectedAt: new Date()
        })
      }
      
      return (originalSetState).apply(this, args)
    }
  }
  private monitorMemoryLeaks() {
    // CRITICAL FIX: Track interval for proper cleanup
    const interval = setInterval(() => {
      const currentMemory = this.getMemoryUsage()
      const memoryIncrease = currentMemory - this.memoryBaseline

      // If memory increased by more than 50MB
      if (memoryIncrease > 50 * 1024 * 1024) {
        this.reportIssue({
          type: 'memory-leak',
          severity: 'high',
          component: 'Application',
          description: `Memory usage increased by ${(Math).round(memoryIncrease / 1024 / 1024)}MB`,
          impact: 'Causes browser slowdown, potential crashes on mobile devices',
          fix: 'Check for uncleaned event listeners, observers, intervals, and closures',
          detectedAt: new Date()
        })

        this.memoryBaseline = currentMemory
      }
    }, 30000) // Check every 30 seconds

    this.monitoringIntervals.add(interval)
  }
  private monitorObserverLeaks() {
    // Intercept PerformanceObserver creation
    const originalObserver = window.PerformanceObserver
    const self = this
    
    window.PerformanceObserver = class extends originalObserver {
      constructor(...args: any[]) {
        super(...args)
        (self).observerCount++
        
        if ((self).observerCount > 10) {
          (self).reportIssue({
            type: 'memory-leak',
            severity: 'high',
            component: 'PerformanceObserver',
            description: `${(self).observerCount} PerformanceObserver instances created`,
            impact: 'Memory leaks, performance degradation',
            fix: 'Ensure observers are disconnected in useEffect cleanup',
            detectedAt: new Date()
          })
        }
      }
      disconnect() {
        (self).observerCount--
        return super.disconnect()
      }
    }
  }
  private monitorStaleClosures() {
    // This is a simplified detection - in practice, this would be more complex
    const originalSetTimeout = window.setTimeout
    const originalSetInterval = window.setInterval
    const self = this
    window.setTimeout = function(callback: Function, delay?: number, ...args: any[]) {
      (self).timeoutCount++
      const wrappedCallback = function() {
        (self).timeoutCount--
        return (callback).apply(this, arguments)
      }
      
      return (originalSetTimeout).call(this, wrappedCallback, delay, ...args)
    }
    window.setInterval = function(callback: Function, delay?: number, ...args: any[]) {
      (self).intervalCount++
      
      if ((self).intervalCount > 5) {
        (self).reportIssue({
          type: 'memory-leak',
          severity: 'medium',
          component: 'Intervals',
          description: `${(self).intervalCount} active intervals detected`,
          impact: 'Potential memory leaks and performance issues',
          fix: 'Ensure intervals are cleared in useEffect cleanup',
          detectedAt: new Date()
        })
      }
      const wrappedCallback = function() {
        return (callback).apply(this, arguments)
      }
      
      return (originalSetInterval).call(this, wrappedCallback, delay, ...args)
    }
  }
  private reportIssue(issue: PerformanceIssue) {
    this.issues.push(issue)
    
    // Log to console in development
    if ((process).env.NODE_ENV === 'development') {
      console.group(`🚨 ${(issue).severity.toUpperCase()} PERFORMANCE ISSUE`)
      console.error(`Component: ${(issue).component}`)
      console.error(`Type: ${(issue).type}`)
      console.error(`Description: ${(issue).description}`)
      console.error(`Impact: ${(issue).impact}`)
      console.error(`Fix: ${(issue).fix}`)
      console.groupEnd()
    }
    
    // Send to analytics in production
    if ((process).env.NODE_ENV === 'production') {
      this.sendToAnalytics(issue)
    }
  }
  private sendToAnalytics(issue: PerformanceIssue) {
    // Send to your analytics service
    try {
      fetch('/api/performance-issues', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(issue)
      }).catch(console.error)
    } catch (error) {
      console.error('Failed to send performance issue to analytics:', error)
    }
  }
  public getIssues(): PerformanceIssue[] {
    return [...this.issues]
  }
  public clearIssues() {
    this.issues = []
  }
  public getStats() {
    return {
      totalIssues: this.issues.length,
      criticalIssues: this.issues.filter(i => (i).severity === 'critical').length,
      memoryUsage: this.getMemoryUsage(),
      observerCount: this.observerCount,
      intervalCount: this.intervalCount,
      timeoutCount: this.timeoutCount
    }
  }

  // CRITICAL FIX: Cleanup method to prevent memory leaks
  public cleanup(): void {
    this.monitoringIntervals.forEach(interval => clearInterval(interval))
    this.monitoringIntervals.clear()
    this.issues = []
    this.renderCounts.clear()
    console.log('🧹 AdvancedBugDetector: Cleanup completed')
  }
}

// Global instance
export const bugDetector = new AdvancedBugDetector()

// React Hook for components to report issues
export function usePerformanceIssueDetection(componentName: string) {
  const renderCount = React.useRef(0)
  const lastRenderTime = React.useRef(Date.now())
  
  // REMOVED: Duplicate render tracking - already handled in monitorRenderLoop method above
  
  return {
    reportIssue: (issue: Omit<PerformanceIssue, 'detectedAt'>) => {
      bugDetector.reportIssue({ ...issue, detectedAt: new Date() })
    }
  }
}

export default AdvancedBugDetector
