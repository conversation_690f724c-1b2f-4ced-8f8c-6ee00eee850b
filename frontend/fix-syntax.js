#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Common syntax error patterns and their fixes
const fixes = [
  // Fix malformed function calls with "( as any)"
  {
    pattern: /\(\s*as\s+any\s*\)/g,
    replacement: '()',
    description: 'Fixed malformed function calls'
  },
  
  // Fix malformed function parameters
  {
    pattern: /\(\s*([^)]*?)\s*as\s+any\s*\)/g,
    replacement: '($1)',
    description: 'Fixed function parameters'
  },
  
  // Fix URLSearchParams constructor
  {
    pattern: /new URLSearchParams\(\s*as\s+any\s*\)/g,
    replacement: 'new URLSearchParams()',
    description: 'Fixed URLSearchParams constructor'
  },
  
  // Fix useCallback with malformed parameters
  {
    pattern: /useCallback\(\(\s*as\s+any\s*\)\s*=>/g,
    replacement: 'useCallback(() =>',
    description: 'Fixed useCallback parameters'
  },
  
  // Fix useEffect with malformed parameters
  {
    pattern: /useEffect\(\(\s*as\s+any\s*\)\s*=>/g,
    replacement: 'useEffect(() =>',
    description: 'Fixed useEffect parameters'
  },
  
  // Fix function declarations with malformed parameters
  {
    pattern: /function\s+(\w+)\(\s*as\s+any\s*\)/g,
    replacement: 'function $1()',
    description: 'Fixed function declarations'
  },
  
  // Fix arrow functions with malformed parameters
  {
    pattern: /=>\s*\(\s*as\s+any\s*\)\s*=>/g,
    replacement: '=> () =>',
    description: 'Fixed arrow functions'
  },
  
  // Fix method calls with malformed parameters
  {
    pattern: /\.(\w+)\(\s*as\s+any\s*\)/g,
    replacement: '.$1()',
    description: 'Fixed method calls'
  },
  
  // Fix type assertions in function parameters
  {
    pattern: /\(\s*([^)]+?)\s+as\s+any\s*\)/g,
    replacement: '($1)',
    description: 'Fixed type assertions in parameters'
  },
  
  // Remove excessive "as any" type assertions
  {
    pattern: /\s+as\s+any/g,
    replacement: '',
    description: 'Removed excessive type assertions'
  },
  
  // Remove @ts-ignore comments that are no longer needed
  {
    pattern: /\s*\/\/\s*@ts-ignore\s*\n/g,
    replacement: '\n',
    description: 'Removed unnecessary @ts-ignore comments'
  },

  // Fix malformed comments with closing braces
  {
    pattern: /\s*\/\/\s*@ts-ignore\s*\)\s*}/g,
    replacement: '}',
    description: 'Fixed malformed @ts-ignore comments with closing braces'
  },

  // Fix malformed comments with closing parentheses
  {
    pattern: /\s*\/\/\s*@ts-ignore\s*\)/g,
    replacement: '',
    description: 'Fixed malformed @ts-ignore comments with closing parentheses'
  },

  // Fix wrapped super calls
  {
    pattern: /\(super\)/g,
    replacement: 'super',
    description: 'Fixed wrapped super calls'
  },

  // Fix wrapped this calls
  {
    pattern: /\(this\)/g,
    replacement: 'this',
    description: 'Fixed wrapped this calls'
  },

  // Fix wrapped console calls
  {
    pattern: /\(console\)/g,
    replacement: 'console',
    description: 'Fixed wrapped console calls'
  },

  // Fix wrapped JSON calls
  {
    pattern: /\(JSON\)/g,
    replacement: 'JSON',
    description: 'Fixed wrapped JSON calls'
  },

  // Fix wrapped window calls
  {
    pattern: /\(window\)/g,
    replacement: 'window',
    description: 'Fixed wrapped window calls'
  },

  // Fix wrapped React calls
  {
    pattern: /\(React\)/g,
    replacement: 'React',
    description: 'Fixed wrapped React calls'
  },

  // Fix missing closing parentheses in cn() calls
  {
    pattern: /cn\([^)]+"\s*>\s*$/gm,
    replacement: (match) => match.replace('>', ')}>'),
    description: 'Fixed missing closing parentheses in cn() calls'
  },

  // Fix specific pattern: "flex-wrap">
  {
    pattern: /"flex-wrap">\s*$/gm,
    replacement: '"flex-wrap")}>',
    description: 'Fixed specific flex-wrap pattern'
  }
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let fixCount = 0;
    
    for (const fix of fixes) {
      const matches = content.match(fix.pattern);
      if (matches) {
        content = content.replace(fix.pattern, fix.replacement);
        fixCount += matches.length;
      }
    }
    
    if (fixCount > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${fixCount} syntax errors in ${path.relative(process.cwd(), filePath)}`);
      return fixCount;
    }
    
    return 0;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return 0;
  }
}

function findTsxFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
console.log('🚀 Starting comprehensive syntax fix...');

const srcDir = path.join(__dirname, 'src');
const files = findTsxFiles(srcDir);

console.log(`📁 Found ${files.length} TypeScript files to process`);

let totalFixes = 0;
let processedFiles = 0;

for (const file of files) {
  const fixes = fixFile(file);
  if (fixes > 0) {
    processedFiles++;
    totalFixes += fixes;
  }
}

console.log(`\n🎉 Completed! Fixed ${totalFixes} syntax errors across ${processedFiles} files`);

if (totalFixes > 0) {
  console.log('\n📋 Next steps:');
  console.log('1. Run "npm run build" to check for remaining errors');
  console.log('2. Fix any remaining TypeScript errors manually');
  console.log('3. Test the application thoroughly');
}
